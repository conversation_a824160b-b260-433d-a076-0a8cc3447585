package com.suncent.smc.persistence.publication.service;

import com.suncent.smc.persistence.aplus.domain.dto.NotRelationAsinDto;
import com.suncent.smc.persistence.aplus.domain.vo.NotRelationAsinVO;
import com.suncent.smc.persistence.publication.domain.dto.HeadPublishUpdateFailGoods;
import com.suncent.smc.persistence.publication.domain.dto.ListingDTO;
import com.suncent.smc.persistence.publication.domain.dto.ScheduledPublishDTO;
import com.suncent.smc.persistence.publication.domain.dto.VCPriceDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.VcLinkErrorDataVO;
import com.suncent.smc.persistence.publication.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SMC-商品主Service接口
 *
 * <AUTHOR>
 * @date 2023-01-12
 */
public interface IGoodsHeadService {
    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    GoodsHead selectListingGoodsHeadById(Integer id);

    /**
     * 查询店铺编码
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    String selectShopCodeById(Integer id);

    /**
     * 查询SMC-商品主列表
     *
     * @param listingGoodsHead SMC-商品主
     * @return SMC-商品主集合
     */
    List<GoodsHead> selectListingGoodsHeadList(GoodsHead listingGoodsHead);

    /**
     * 查询SMC-商品主列表
     *
     * @param listingGoodsHead SMC-商品主
     * @return SMC-商品主集合
     */
    List<GoodsHeadVO> selectListingGoodsHeadVOList(GoodsHead listingGoodsHead);

    List<GoodsHeadVO> selectListingGoodsHeadVOListCount(GoodsHead listingGoodsHead);

    /**
     * 新增SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    int insertListingGoodsHead(GoodsHead listingGoodsHead);

    /**
     * 修改SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    int updateListingGoodsHead(GoodsHead listingGoodsHead);
    /**
     * 修改SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    int updateListingGoodsHeadByIdList(GoodsHead listingGoodsHead);

    /**
     * 批量删除SMC-商品主
     *
     * @param ids 需要删除的SMC-商品主主键集合
     * @return 结果
     */
    int deleteListingGoodsHeadByIds(String ids);

    /**
     * 删除SMC-商品主信息
     *
     * @param id SMC-商品主主键
     * @return 结果
     */
    int deleteListingGoodsHeadById(Integer id);

    /**
     * 删除SMC-商品主信息
     *
     * @param ids SMC-商品主主键
     * @return 结果
     */
    int deleteListingGoodsHeadByIdList(String ids);

    int deleteListingGoodsHeadByIds(Integer[] ids);
    /**
     * 批量下架Listing
     *
     * @param ids 需要下架的Listing主键
     * @return 结果
     */
    int stopPublishListingByIds(String ids);

    /**
     * 批量刊登Listing
     *
     * @param ids SMC-商品主主键
     * @return 结果
     */
    int publishListingByIds(String ids);

    /**
     * 查询商品头部基础数据集合
     *
     * @param ids 需要查询的Listing主键
     * @return SMC-商品集合
     */
    List<GoodsHead> selectListingGoodsHeadByIds(@Param("ids") Integer[] ids);

    /**
     * 批量新增SMC-商品主
     *
     * @param goodsHeadList SMC-商品主
     * @return 结果
     */
    int insertListingGoodsHeads(@Param("goodsHeadList") List<GoodsHead> goodsHeadList);

    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    Map<String, String> selectGoodsHeadMapById(Integer id);


    GoodsHead getGoodsHeadParam(ListingDTO listingDTO);

    /**
     * 获取AM没有asin的商品
     * @return
     */
    List<GoodsHead> selectAmGoodsHeads(GoodsHead goodsHead);

    /**
     * 更新库存为0 状态为更新中
     * 针对在线商品
     * @return
     */
    List<GoodsHead> selectListingByPdmGoodsCode(String[] codes);

    /**
     * 更新库存为0 状态为更新中
     * 针对在线商品
     * @return
     */
    int updateListingByIds(String[] codes);


    String scheduledPublishToShop(ScheduledPublishDTO dto);


    int updateListingPublishStatusByIds(Integer[] ids);
    /**
     * 查询Listing主表
     * @param goodsHead
     * @Date 2023/6/15 16:38
     * @return
     * <AUTHOR>
     */
    List<GoodsHeadVO> getListingGoodsHeadVOList(GoodsHead goodsHead);

    /**
     * 通过标题查询Listing主表
     * @param goodsHead
     * @return
     */
    Integer selectCountByTitle(GoodsHead goodsHead);

    List<GoodsHeadHomePageVO> getGroundingListing(GoodsHeadHomePageVO goodsHeadHomePageVO);

    List<GoodsHeadHomePageVO> getSalesingListing(GoodsHeadHomePageVO goodsHeadHomePageVO);

    List<GoodsHeadHomePageVO> getOffListing(GoodsHeadHomePageVO goodsHeadHomePageVO);

    /**
     * 查询当前店铺 该sku 自动刊登有多少listing
     * @param queryExist
     * @return
     */
    Integer selectCountByAutoSku(GoodsHead queryExist);

    /**
     * 根据pdm商品编码查询listing
     * @param goodsHead
     * @return
     */
    List<GoodsHead> selectListBySkuList(GoodsHead goodsHead);

    /**
     * 查询在售的listing
     * @param platform
     * @return
     */
    List<GoodsHead> selectOnlineListing(String platform);
    /**
     * 查询在售的listing
     * @return
     */
    List<GoodsHead> selectOnlineListing();

    List<GoodsHead> selectOnlineListing(String shopCode, List<String> skus);

    List<GoodsHead> selectAllListing(String shopCode, List<String> skus);

    /**
     * 查询asin不为空的listing
     * @param platform ,shopCode
     * @return
     */
    List<GoodsHead> selectOnlineListingByPlatfromAndShopCode(String platform,String shopCode);

    List<String> selectGoodsCodeSale(String userName);

    List<String> selectAllGoodsCode();
    int deleteListingGoodsHeadByIds(String remark, Integer[] ids);
    List<GoodsHeadVO> listingGoodsHeadVOList(Integer[] idArray);

    List<Integer> selectSaleAndOrderList(ListingDTO listingDTO);

    int clearPlatformGoodId(GoodsHead goodsHead);

    List<GoodsHead>  selectNewOnlineTimeByShopCodeAndSize(List<String> platformItemIds, @Param("shopCode") String shopCode, @Param("size") Long size);

    List<GoodsHead>  selectHeadByShopCodeAndShopCategory(String shopCode,String shopCategory);

    /**
     * 查询失败状态的listing
     * @return
     */
    List<GoodsHead> selectFailListing();

    /**
     * 逻辑删除刊登失败超过1个月的listing
     * @return
     */
    int deleteFailListing();


    List<GoodsHead> selectHeadListByShopAndTitle(GoodsHead listingGoodsHead);

    List<GoodsHead> selectHeadsByPublishStatusAndUpdateTime(HeadPublishUpdateFailGoods dto);

    GoodsHead queryLatestListing(List<String> sku);

    List<GoodsHead> selectListByPlatformGoodsCodes(List<String> platformGoodsCodes);

    List<GoodsHead> selectListInventoryNoZeroBySkuList(GoodsHead query);

    List<GoodsHeadVO> selectListingGoodsByShopAndSku(List<String> shopCodeList, List<String> skus,Long userId);

    List<String> selectGoodsCodeSaleByShop(List<String> shopCodeList,Long userId);

    List<GoodsHeadVO>  selectListingUpdateFailureByShop(List<String> shopCodeList,Long userId);


    List<GoodsHead> selectListingZeroQty(String platform, String site, String shopCode);

    List<NotRelationAsinVO> selectNotRelationAPlusListing(NotRelationAsinDto notRelationAsinDto);

    int updataAsinRefundRateLabel(String platform, String platformSaleCode, String refundRateLabel);

    int updataSkuRefundRateLabel(String platform, String sku, String refundRateLabel);

    List<GoodsHead> selectFailTodoListing(GoodsHead goodsHead);

    Integer countByPublishFailByShopCodes(List<String> shopCodeList);

    List<String> selectAsinByShopCodeAndAsinIsNotNull(String shopCode);

    /**
     * 根据亚马逊报告修改 am库存价格状态
     * @param quantity
     * @param price
     * @param publishStatus
     * @param sellerSku
     * @param asin
     * @param shopCode
     * @return
     */
    int updateByAMReport(String quantity, String price, Integer publishStatus, String sellerSku, String asin,String shopCode);

    List<GoodsHeadVO> selectListingGoodsByShopCodeAndSku(List<String> shopCodes, String  platformSku, String goodsCode);

    Integer countListingGoodsByUserAndSku(List<String> shopCodes, String platformSku, String goodsCode);

    Integer batchUpdateSettlementPrice(List<GoodsHead> goodsHeadList);

    Integer batchUpdateSettlementPriceV2();
    Integer batchUpdateRedlinePrice(List<GoodsHead> goodsHeadList);

    Integer batchUpdateRedlinePriceV2(List<GoodsHead> goodsHeadList);

    Integer updateRedLinePrice(BigDecimal redLinePrice, Integer publishType, String pdmGoodsCode);

    List<GoodsHead> selectPriceTodoListing();

    List<GoodsHeadPriceVo> getGoodsHeadPriceVoList(List<String> goodsCodeList);

    /**
     * 清空原价
     * @param goodsHead
     * @return
     */
    int clearOriginalPrice(GoodsHead goodsHead);


    List<Integer> selectListIds(GoodsHead listingGoodsHead);

    List<GoodsHeadVO> countGoodsHeadVOList(GoodsHead goodsHeadParam);

    /**
     * 修改运营字段
     * @param operators
     * @param sitCode
     * @param shopCode
     * @param platformSku
     * @param goodsCode
     * @return
     */
    int updateOperators(String operators, String sitCode, String shopCode, String platformSku, String goodsCode);

    List<GoodsHeadCountVO> countOnlineListing(List<String> skus);

    /**
     * 获取am 相同asin的listing 并创建临时表
     * @return
     */
    int createTempTableAndInsertData();

    int clearAMainTable();

    /**
     * 根据临时表更新主表   isMain字段
     * @return
     */
    int updateMainTable();

    /**
     * 删除临时表
     * @return
     */
    int dropTempTable();

    /**
     * 根据店铺出查询对应头表数据
     * @param combinedShopCodeList
     * @return
     */
    List<GoodsHeadVO> selectListingByShopCode(List<String> combinedShopCodeList);

    int selectListingCount(List<Integer> goodsId);

    List<GoodsHead> selectAmListingByPlatformGoodsIdList(List<String> asinList);

    int updateZeroInventory(List<Integer> goodsId);

    List<GoodsHeadCountVO> countOnlineListingGroup(List<String> goodsCodes);

    /**
     * 查询AM的在售listing，通过asin和平台sku
     *
     * @param
     * @param shopCode
     * @param sitCode
     * @return
     */
    List<GoodsHead> selectAMOnlineListingByAsinAndPlatformSku(String asin, String platformSku, String shopCode, String sitCode);

    List<GoodsHead> selectOnlineListingByAsinAndPlatformSku(String asin, String platformSku, String shopCode, String sitCode, String platformCode);

    int updateHeadsAdapterByItemId(List<GoodsHead> goodsHeads);

    List<GoodsHead> listNeedUpdateItemsPerInnerPackByLastId(Integer publishType, List<String> platformSkus, Integer lastId);

    List<GoodsHead> listNeedSyncRealTimeSales(List<String> platformGoodsCodes);

    List<GoodsHead> selectVCDFListing(List<String> sellerSkuList);

    int updateInventoryByGoodsId(Long goodsId, Integer inventory);

    int updateVcInventoryBySellerSku(List<String> sellerSkuLit);

    List<GoodsHead> selectVCDFListingBySku(List<String> skuList, String shopCode);


    List<GoodsHead> listFailListing(String userId);

    /**
     * 查询已经跟卖过的listing
     * @param publishType
     * @param goodsId
     * @return
     */
    List<String> listFollowSoldAsin(String publishType, List<String> goodsId);


    List<GoodsHead> selectDeleteListingGoodsHeadList(String sellerSku, String shopCode, Integer publishType, String asin, Integer day);

    /**
     * 更新状态为适配中，需要原来状态是待适配或为null，只处理AM的listing
     * @param goodsHeads
     * @return
     */
    void updateHeads(List<GoodsHead> goodsHeads);

    /**
     * 查询需要适配的listing
     * @param lastId
     * @return
     */
    List<GoodsHead> listWaitAdaptByLastId(Integer lastId);

    /**
     * 查询需要获取系列的listing
     * @param lastId
     * @return
     */
    List<GoodsHead> listNeedSyncSeriesByLastId(Integer lastId);

    GoodsHead selectLastGoodsHeadByAsin(String followAsin, Integer id);

    /**
     * 查询需要更新在售状态的VC listing
     * @return
     */
    List<GoodsHead> listNeedUpdatePublishStatus(Integer lastId);

    String getFollowVcPoAsin(GoodsHead goodsHead);

    int countByPlatformGoodsCodeAndShopCodeAndPublishType(String platformSku, String shopCode, Integer publishType, Integer id);

    List<GoodsHead> selectAMList(String createBy, String shopCode, String categoryId);

    List<GoodsHead> selectGoodsHeadByShopCodeAndAsin(String shopCode, List<String> asinList);

    List<GoodsHead> selectScAsinList(Integer minId, Integer maxId);

    Integer selectMaxIdBefore270Days();

    void insertScAsinList(List<String> asinList);

    List<String> listTempAsinList();

    void deleteTempAsinList(List<String> alreadyHandleAsinList);

    List<GoodsHead> selectGoodsHeadByAsin(List<String> asinList, String platform);

    void deleteTempAsinListAll();

    Long getNeedBackupStartId();

    List<GoodsHead> listNeedBackup(Long lastId, Long maxId);

    List<GoodsHeadCountVO> listMainBrandListing(List<String> goodsCodeList, List<String> mainBrandList);

    List<GoodsHead> selectAMListingByBrand(String brand, Long lastId, Date beforeDate);

    List<GoodsHead> selectGoodsHeadByShopCodeAndPlatformGoodsCode(String shopCode,String publishType, List<String> platformSkus);

    void handleQueryNew(ListingDTO listingDTO);


    List<VcLinkErrorDataVO> listVcErrorData(Long userId, String period);

    List<GoodsHead> queryNeedPullEbayListings();


    List<GoodsHead> queryNeedPullAmazonListings();

    void updateListingOffSale(String itemId, String accountCode, Integer publishStatus);

    List<GoodsHead> listByPlatformAndShopCode(String platform, String shopCode, List<String> platformSkuList);

    List<VCPriceVO> queryMonitorPoolVCPrice(List<VCPriceDTO> vcPriceDTOS);

    void updateGoodHeadMain();

    Integer getPublishType(Integer headId);

    /**
     * 根据ASIN获取品牌信息
     * 
     * @param asin ASIN
     * @return 品牌编码
     */
    String selectBrandByAsin(String asin);

    void insertTempRedLineWhiteSkus(List<String> pdmGoodsCodes);

    /**
     * 根据ASIN查询其他链接
     *
     * @param asin ASIN值
     * @param currentHeadId 当前商品ID（排除此ID）
     * @return 其他链接信息列表
     */
    List<GoodsHead> selectOtherListingsByAsin(String asin, Long currentHeadId);

    /**
     * 清空临时ASIN销量表
     */
    void clearTempAsinSalesTable();

    void insertAsinSales(List<String> asinList);

    List<String> listAsinSales(int batchSize);

    /**
     * 物理删除临时表中已处理的ASIN列表
     * @param asinList ASIN列表
     */
    void deleteAsinSales(List<String> asinList);

    String selectListingGoodsHeadByAsinAndShopCode(String asin, String shopCode, String sku, Integer publishType);
}