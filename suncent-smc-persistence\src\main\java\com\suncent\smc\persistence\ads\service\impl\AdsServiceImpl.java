package com.suncent.smc.persistence.ads.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.persistence.ads.domain.*;
import com.suncent.smc.persistence.ads.mapper.AdsItemFitCompareMapper;
import com.suncent.smc.persistence.ads.mapper.AdsMapper;
import com.suncent.smc.persistence.ads.service.IAdsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdsServiceImpl implements IAdsService, InitializingBean {

    @Autowired
    private AdsMapper adsMapper;
    @Autowired
    private CacheManager cacheManager;
    private Cache<String, List<Map<String, Object>>> adsFitmentDataCache;
    @Autowired
    private AdsItemFitCompareMapper adsItemFitCompareMapper;

    @Override
    public void afterPropertiesSet() throws Exception {
        QuickConfig stockConfig = QuickConfig.newBuilder("adsService:countByProductCodes").expire(Duration.ofHours(12)).localLimit(1000).cacheType(CacheType.REMOTE).cacheNullValue(true).build();
        adsFitmentDataCache = cacheManager.getOrCreateCache(stockConfig);
    }

    @Override
    public Integer saveToAdsItDemandA(ItDemand itDemand) {
        return adsMapper.insertItDemandA(itDemand);
    }


    @Override
    public Integer saveToAdsItDemandCompare(ItDemand itDemand) {
        return adsMapper.insertItDemandCompare(itDemand);
    }

    @Override
    public Integer countByPnAndAsin(  String asin) {
        if (StrUtil.isBlank(asin)) {
            return 0;
        }
        return adsMapper.countByPnAndAsin( asin);
    }


    @Override
    public Integer countByPnAndAsinCompare(  String asin) {
        if (StrUtil.isBlank(asin)) {
            return 0;
        }
        return adsMapper.countByPnAndAsinCompare( asin);
    }

    @Override
    public List<Map<String, String>> getStatusByAsin(List<String> asinList) {
        return adsMapper.selectByAsinList(asinList);
    }


    @Override
    public List<Map<String, Object>> selectAdapterErrorList(int currentNumber) {
        return adsMapper.selectAdapterErrorList(currentNumber);
    }


    @Override
    public Integer insertItDemandByCopyId(String partNumber, String itDemandId) {
        return adsMapper.insertItDemandByCopyId(partNumber, itDemandId);
    }

    @Override
    public List<Map<String, String>> selectByStatus(List<String> status, String createTime) {
        return adsMapper.selectByStatus(status,createTime);
    }

    @Override
    public int updateItDemandStatusByAsin(List<String> asinList,String status,String errorReason) {
        return adsMapper.updateItDemandStatusByAsin(asinList,status,errorReason);
    }


    @Override
    public int updateItDemandStatusById(String id,String status,String errorReason) {
        return adsMapper.updateItDemandStatusById(id,status,errorReason);
    }

    @Override
    public List<AdsFitmentDataEbay> selectFitmentDataEbayByProductCode(String productCode, String goodsCode) {
        //Ebay sku 先过一遍 dic_partnumber表,无数据则继续,有数据替换继续
        String originalPartNumber = adsMapper.selectDicPartnumberByProductCode(productCode);
        if (ObjectUtils.isNotEmpty(originalPartNumber)) {
            productCode = originalPartNumber;
        }
        List<AdsFitmentDataEbay> dataEbayList = adsMapper.selectFitmentDataEbayByProductCode(productCode);
        if (CollectionUtils.isEmpty(dataEbayList)) {
            List<AdsEbayDataRequirement> requirement = adsMapper.selectEbayDataRequirementByProductCode(productCode, goodsCode);
            if (CollectionUtils.isEmpty(requirement)) {
                adsMapper.insertEbayDataRequirement(productCode, goodsCode);
            }
        }
        //判断数据是否完整
        AtomicBoolean isComplete = new AtomicBoolean(true);
        dataEbayList.parallelStream().forEach(data -> {
            if (ObjectUtils.isEmpty(data.getMake()) || ObjectUtils.isEmpty(data.getYear()) || ObjectUtils.isEmpty(data.getModel())) {
                isComplete.set(false);
                return;
            }
            if (ObjectUtils.isEmpty(data.getSubmodel())) {
                if (ObjectUtils.isEmpty(data.getTrim()) || ObjectUtils.isEmpty(data.getEngine())) {
                    isComplete.set(false);
                    return;
                }
            }
        });
        if (!isComplete.get()) {
            List<AdsEbayDataRequirement> requirement = adsMapper.selectEbayDataRequirementByProductCode(productCode, goodsCode);
            if (CollectionUtils.isEmpty(requirement)) {
                adsMapper.insertEbayDataRequirement(productCode, goodsCode);
            }
        }
        return dataEbayList;
    }

    @Override
    public Integer updateEbayFitmentIsUpload(List<String> productCodeList) {
        return adsMapper.updateEbayFitmentIsUpload(productCodeList);
    }

    @Override
    public List<AdsEbayDataRequirement> selectEbayDataRequirementByProductCode(String productCode, String goodsCode) {
        return adsMapper.selectEbayDataRequirementByProductCode(productCode, goodsCode);
    }

    @Override
    public List<AdsFitmentDataVIO> selectFitmentDataVIO(List<String> productCodes) {
        return adsMapper.selectFitmentDataVIO(productCodes);
    }

    @Override
    public List<AdsFitmentGPTData> selectFitmentGPTData(List<String> productCodes) {
        return adsMapper.selectFitmentGPTData(productCodes);
    }

    @Override
    public AdsFitmentCombinationDTO handleAdsDataForExport(List<AdsFitmentDataVIO> adsEbayDataRequirementList, List<String> groupParamList) {
        if (ObjectUtils.isEmpty(adsEbayDataRequirementList)||ObjectUtils.isEmpty(groupParamList)) {
            return null;
        }

        List<AdsFitmentDataVIO> vioList = this.group2List(adsEbayDataRequirementList,groupParamList);
        //vioList按照vio倒序
        vioList = vioList.stream().sorted(Comparator.comparing(AdsFitmentDataVIO::getVio).reversed()).collect(Collectors.toList());

        AdsFitmentCombinationDTO export = new AdsFitmentCombinationDTO();
        export.setProductCode(adsEbayDataRequirementList.get(0).getProductCode());
        export.setContent0(getContent(vioList, 0));
        export.setContent1(getContent(vioList, 1));
        export.setContent2(getContent(vioList, 2));
        export.setContent3(getContent(vioList, 3));
        export.setContent4(getContent(vioList, 4));
        return export;
    }

    @Override
    public String getDataMaxBatch() {
        return adsMapper.getDataRedLineMaxBatch();
    }

    @Override
    public List<Map<String, String>> getRedLinePriceList(String dataMaxBatch,List<String> goodsCodeList,String platform) {
        return adsMapper.getRedLinePriceList(dataMaxBatch,goodsCodeList,platform);
    }

    @Override
    public List<String> getGoodsCodeList(String dataMaxBatch) {
        return adsMapper.getDataRedLineGoodsByMaxBatch(dataMaxBatch);
    }

    @Override
    public Map<String, Map<String, Object>> getAdaptInfoMap(String productCode, List<String> paramNames, Map<String, Object> globalContext) {
        List<AdsFitmentDataVIO> adsFitmentDataVIOS = null;
        if (globalContext.containsKey("adsFitmentData")) {
            adsFitmentDataVIOS = (List<AdsFitmentDataVIO>) globalContext.get("adsFitmentData");
            if (CollectionUtil.isEmpty(adsFitmentDataVIOS)) {
                return null;
            }
        }else {
            adsFitmentDataVIOS = adsMapper.selectFitmentDataVIO(Arrays.asList(productCode));
            if (CollectionUtil.isEmpty(adsFitmentDataVIOS)) {
                return null;
            }
            globalContext.put("adsFitmentData", adsFitmentDataVIOS);
        }


        Map<String, Map<String, Object>> result = new HashMap<>();

        List<AdsFitmentDataVIO> vioList = this.group2ListByWordParts(adsFitmentDataVIOS, paramNames);
        //vioList按照vio倒序
        vioList = vioList.stream().sorted(Comparator.comparing(AdsFitmentDataVIO::getVio).reversed()).collect(Collectors.toList());

        // 转成model, year, liter的map
        Map<String, Object> adsResult = new HashMap<>();

        List<String> modelList = new ArrayList<>();
        List<String> yearList = new ArrayList<>();
        List<String> literList = new ArrayList<>();
        List<String> submodelList = new ArrayList<>();
        List<String> makeList = new ArrayList<>();
        // 位置
        List<String> positionList = new ArrayList<>();
        adsResult.put("modelList", modelList);
        adsResult.put("yearList", yearList);
        adsResult.put("literList", literList);
        adsResult.put("subModelList", submodelList);
        adsResult.put("makeList", makeList);
        adsResult.put("positionList", positionList);

        for (AdsFitmentDataVIO adsFitmentDataVIO : vioList) {
            modelList.add(adsFitmentDataVIO.getModelName());
            yearList.add(adsFitmentDataVIO.getYearName());
            literList.add(adsFitmentDataVIO.getLiter());
            makeList.add(adsFitmentDataVIO.getMakeName());
            submodelList.add(adsFitmentDataVIO.getSubModelName());
            positionList.add(adsFitmentDataVIO.getPosition());
        }

        result.put(productCode, adsResult);
        return result;
    }

    @Override
    public List<AdsListingLabel> getListingLabelList(String shopCode) {
        if (StrUtil.isBlank(shopCode)) {
            log.error("查询ADS的标签数据，店铺编码不能为空" + shopCode);
            return new ArrayList<>();
        }
        // 需要做分页处理，现在VC店铺的数据一次13.8w数据
        int pageNum = 1;
        int pageSize = 1000;
        List<AdsListingLabel> result = new ArrayList<>();
        PageHelper.startPage(pageNum,pageSize);
        List<AdsListingLabel> list = adsMapper.getListingLabelList(shopCode);
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        result.addAll(list);
        // 判断是否还有下一页
        int pages = new PageInfo<>(list).getPages();
        while (pageNum < pages) {
            pageNum++;
            PageHelper.startPage(pageNum,pageSize);
            list = adsMapper.getListingLabelList(shopCode);
            if (CollUtil.isEmpty(list)) {
                break;
            }
            result.addAll(list);
        }
        log.info("店铺的ListingLabel数据分页处理完成，总共{}条数据，分页{}页，每页{}条数据", result.size(), pages, pageSize);
        return result;
    }


    @Override
    public AdsListingLabel getListingLabelByAsin(String shopCode,String asin) {
        return adsMapper.getListingLabelByAsin(shopCode,asin);
    }

    @Override
    public Map<String, List<Map<String, Object>>> countByProductCodes(List<String> productCodes) {
        if(CollUtil.isEmpty(productCodes)) {
            return new HashMap<>();
        }
        Map<String, List<Map<String, Object>>> dataCacheAll = adsFitmentDataCache.getAll(new HashSet<>(productCodes));
        List<String> unCachedProductCodes = productCodes.stream().filter(productCode -> !dataCacheAll.containsKey(productCode)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unCachedProductCodes)) {
            Lists.partition(unCachedProductCodes, 500).forEach(list -> {
                List<Map<String, Object>> data = adsMapper.countByProductCodes(list);
                if (CollUtil.isEmpty(data)) {
                    return;
                }
                Map<String, List<Map<String, Object>>> dataCache = data.stream().collect(Collectors.groupingBy(map -> (String) map.get("product_code")));
                for(String productCode : list) {
                    adsFitmentDataCache.put(productCode, dataCache.get(productCode));
                    dataCacheAll.put(productCode, dataCache.get(productCode));
                }
            });
        }
        return dataCacheAll;
    }

    @Override
    public List<AdsRecordData> selectTodayCartList(String date,String shopCode,List<String> asinList) {
        return adsMapper.selectTodayCartList(date,shopCode,asinList);
    }

    @Override
    public List<Object> handleAdsDataForExportV2(List<AdsFitmentDataVIO> adsList, List<String> columnsOr, String splitStr) {
        if (ObjectUtils.isEmpty(adsList) || ObjectUtils.isEmpty(columnsOr)) {
            return null;
        }
        //将columnsOr 转成一个新的List<String> columns
        List<String> columns = new ArrayList<>();
        columns.addAll(columnsOr);
        if (columns.size() == 1) {
            adsList =  adsList.stream().sorted(Comparator.comparing(AdsFitmentDataVIO::getVio).reversed()).collect(Collectors.toList());
            // 只有一个字段，从adsList中取出该字段的值，去重后返回
            StringBuilder sb = new StringBuilder();

            List<Object> datas = adsList.stream()
                    .map(adsFitmentDataVIO -> {
                        try {
                            Field field = ReflectUtil.getField(AdsFitmentDataVIO.class, columns.get(0));
                            field.setAccessible(true);
                            return field.get(adsFitmentDataVIO);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            for (Object data : datas) {
                // 如果超过单元格上限，不继续追加，直接返回
                if (sb.length() + data.toString().length() > 32767) {
                    break;
                }
                sb.append(data);
                
                if (datas.indexOf(data) != datas.size() - 1) {
                    sb.append(splitStr);
                }
            }
            return Collections.singletonList(sb.toString());
        }

        boolean makeNameBool = !columns.contains("makeName") ? false : true;
        if (!columns.contains("makeName")){
            columns.add("makeName");
        }

        List<AdsFitmentDataVIO> vioList = this.group2ListV2(adsList, columns);
        //vioList按照vio倒序
        vioList = vioList.stream().sorted(Comparator.comparing(AdsFitmentDataVIO::getVio).reversed()).collect(Collectors.toList());

        List<Object> result = new ArrayList<>();
        StringBuilder sb = new StringBuilder();

        Set<String> resultSet = new HashSet<>();
        for (AdsFitmentDataVIO vio : vioList) {
            StringBuilder subSb = new StringBuilder();
            for (String column : columns) {
                try {
                    //不包含makeName 则跳过
                    if (!makeNameBool && "makeName".equals(column)){
                        continue;
                    }
                    Field field = ReflectUtil.getField(AdsFitmentDataVIO.class, column);
                    field.setAccessible(true);
                    Object value = field.get(vio);
                    boolean hasValue = ObjectUtils.isNotEmpty(value) && value instanceof String && StrUtil.isNotBlank((String) value);
                    if(!hasValue) {
                        continue;
                    }
                    String str = (String) value;
                    subSb.append(str);
                    if (columns.indexOf(column) != columns.size() - 1) {
                        subSb.append(" ");
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
            String string = subSb.toString();
            string = StrUtil.trim(string);
            if (!resultSet.contains(string)) {
                if (sb.length() + string.length() > 32767) {
                   break;
                }
                sb.append(string);
                resultSet.add(string);
                // 不是最后一条数据，添加分隔符
                if (vioList.indexOf(vio) != vioList.size() - 1) {
                    sb.append(splitStr);
                }
            }
        }
        result.add(sb.toString());
        return result;
    }

    @Override
    public int countNodeIncorrectByCoreListing(String nowDate) {
        return adsMapper.countNodeIncorrectByCoreListing(nowDate);
    }


    @Override
    public List<AmzJudgeCategoryInfoVO> listNodeIncorrectByCoreListing(String nowDate) {
        return adsMapper.listNodeIncorrectByCoreListing(nowDate);
    }

    @Override
    public void updateCaseIdByNodeCorrect(String platformGoodsId, String platformGoodsCode, String shopCode, String siteCode, String caseId) {
        adsMapper.updateCaseIdByNodeCorrect(platformGoodsId, platformGoodsCode, shopCode, siteCode, caseId);
    }

    @Override
    public void insertAmzJudgeCategoryMap(AmzJudgeCategoryMapVO amzJudgeCategoryMapVO) {
        adsMapper.insertAmzJudgeCategoryMap(amzJudgeCategoryMapVO);
    }

    @Override
    public int countAmzJudgeCategoryMap(String operationClassification,String categoryCode, String newPlatformCategoryId, String newCategoryName) {
        return adsMapper.countAmzJudgeCategoryMap(operationClassification, categoryCode, newPlatformCategoryId, newCategoryName);
    }

    @Override
    public void updateAmzJudgeCategoryInfoStatus(String platformGoodsId, String platformGoodsCode, String shopCode, String siteCode) {
        adsMapper.updateAmzJudgeCategoryInfoStatus(platformGoodsId, platformGoodsCode, shopCode, siteCode);
    }

    private List<AdsFitmentDataVIO> group2ListV2(List<AdsFitmentDataVIO> adsList, List<String> columns ) {
        boolean containYear = columns.contains("year") || columns.contains("yearName");
        List<String> copyColumns = new ArrayList<>(columns);
        // 页面选了年份字段，需要对年份合并，这里先不对年份字段进行分组，方便后面合并
        if (containYear) {
            copyColumns.removeIf(column -> Objects.equals(column, "year") || Objects.equals(column, "yearName"));
        }
        // 如果没有选择年份字段，需要添加年份字段分组，不需要年份合并
        else {
            copyColumns.add("yearName");
        }

        Map<String, List<AdsFitmentDataVIO>> modelMap = adsList.stream().collect(Collectors.groupingBy(adsFitmentDataVIO -> {
            StringBuilder key = new StringBuilder();
            // 利用反射获取属性字段名
            for (String paramName : copyColumns) {
                try {
                    Field field = ReflectUtil.getField(adsFitmentDataVIO.getClass(), paramName);
                    field.setAccessible(true);
                    key.append(field.get(adsFitmentDataVIO)).append("#");
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
            return key.toString();
        }));

        List<AdsFitmentDataVIO> result = new ArrayList<>();
        modelMap.forEach((k, v) -> {

            if (ObjectUtils.isEmpty(v)) {
                return;
            }
            List<String> yearList = new ArrayList<>();


            for (AdsFitmentDataVIO dataVIO : v) {
                //清洗年份
                clenYearData(yearList, dataVIO);
            }

            String yearStr = yearStr(yearList);
            //vio 求和
            BigDecimal sumVio = v.stream()
                    .map(AdsFitmentDataVIO::getVio)
                    .filter(value -> value != null && Utils.checkDoubleNumber(String.valueOf(value)))
                    .reduce((a, b) ->a.add(b))
                    .orElse(BigDecimal.ZERO);

            AdsFitmentDataVIO newDataVio = new AdsFitmentDataVIO();
            columns.forEach(column -> {
                try {
                    Field field = ReflectUtil.getField(AdsFitmentDataVIO.class, column);
                    field.setAccessible(true);
                    field.set(newDataVio, field.get(v.get(0)));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            });

            newDataVio.setYearName(StrUtil.isNotBlank(yearStr) ? StrUtil.trim(yearStr) : null);
            newDataVio.setVio(sumVio);
            result.add(newDataVio);
        });
        return result;
    }

    private List<AdsFitmentDataVIO> group2ListByWordParts(List<AdsFitmentDataVIO> list, List<String> paramNames) {
        // 按照paramNames 动态分组
        Map<String, List<AdsFitmentDataVIO>> modelMap  = list.stream().collect(Collectors.groupingBy(adsFitmentDataVIO -> {
            StringBuilder key = new StringBuilder();
            for (String paramName : paramNames) {
                if (Objects.equals(paramName, "model")) {
                    key.append(adsFitmentDataVIO.getModelName()).append("#");
                } else if (Objects.equals(paramName, "submodel")) {
                    key.append(adsFitmentDataVIO.getSubModelName()).append("#");
                } else if (Objects.equals(paramName, "liter")) {
                    key.append(adsFitmentDataVIO.getLiter()).append("#");
                }else if (Objects.equals(paramName, "make")) {
                    key.append(adsFitmentDataVIO.getMakeName()).append("#");
                }else if (paramName.contains("position")) {
                    key.append(adsFitmentDataVIO.getPosition()).append("#");
                }
            }
            return key.toString();
        }));

        List<AdsFitmentDataVIO> result = new ArrayList<>();
        modelMap.forEach((k, v) -> {

            if (ObjectUtils.isEmpty(v)) {
                return;
            }
            List<String> yearList = new ArrayList<>();


            for (AdsFitmentDataVIO dataVIO : v) {
                //清洗年份
                clenYearData(yearList, dataVIO);
            }
            AdsFitmentDataVIO newDataVio = new AdsFitmentDataVIO();

            String yearStr = yearStr(yearList);
            //vio 求和
            BigDecimal sumVio = v.stream()
                    .map(AdsFitmentDataVIO::getVio)
                    .filter(value -> value != null && Utils.checkDoubleNumber(String.valueOf(value)))
                    .reduce((a, b) ->a.add(b))
                    .orElse(BigDecimal.ZERO);

            newDataVio.setModelName(v.get(0).getModelName());
            newDataVio.setSubModelName(v.get(0).getSubModelName());
            newDataVio.setLiter(v.get(0).getLiter());
            newDataVio.setMakeName(v.get(0).getMakeName());
            newDataVio.setPosition(v.get(0).getPosition());
            newDataVio.setYearName(yearStr);
            newDataVio.setVio(sumVio);
            result.add(newDataVio);

        });
        return result;
    }

    /**
     * 按照用户页面勾选参数进行分组
     * yearName存在包含的关系的数据需要合并
     * <p>
     * yearName 暂时有2001-2003,2005-2006  ||2007 || 1999-2006   三种格式
     *
     * @param adsEbayDataRequirementList
     * @return
     */
    private List<AdsFitmentDataVIO> group2List(List<AdsFitmentDataVIO> adsEbayDataRequirementList, List<String> groupParamList) {
        Map<String, List<AdsFitmentDataVIO>> modelMap = adsEbayDataRequirementList.stream().collect(Collectors.groupingBy(adsFitmentDataVIO -> {
            StringBuilder key = new StringBuilder();
            for (String paramName : groupParamList) {
                if (Objects.equals(paramName, "model")) {
                    key.append(adsFitmentDataVIO.getModelName()).append("#");
                } else if (Objects.equals(paramName, "year")) {
                    key.append(adsFitmentDataVIO.getYearName()).append("#");
                } else if (Objects.equals(paramName, "liter")) {
                    key.append(adsFitmentDataVIO.getLiter()).append("#");
                }else if (Objects.equals(paramName, "make")) {
                    key.append(adsFitmentDataVIO.getMakeName()).append("#");
                }
            }
            return key.toString();
        }));

        List<AdsFitmentDataVIO> result = new ArrayList<>();
        modelMap.forEach((k, v) -> {

            if (ObjectUtils.isEmpty(v)) {
                return;
            }
            List<String> yearList = new ArrayList<>();


            for (AdsFitmentDataVIO dataVIO : v) {
                //清洗年份
                clenYearData(yearList, dataVIO);
            }
            AdsFitmentDataVIO newDataVio = new AdsFitmentDataVIO();

            String yearStr = yearStr(yearList);
            //vio 求和
            BigDecimal sumVio = v.stream()
                    .map(AdsFitmentDataVIO::getVio)
                    .filter(value -> value != null && Utils.checkDoubleNumber(String.valueOf(value)))
                    .reduce((a, b) ->a.add(b))
                    .orElse(BigDecimal.ZERO);

            newDataVio.setModelName(v.get(0).getModelName());
            newDataVio.setYearName(yearStr);
            newDataVio.setVio(sumVio);
            newDataVio.setLiter(v.get(0).getLiter());
            result.add(newDataVio);

        });
        return result;
    }

    public static String yearStr(List<String> yearList) {
        //yearList 去重 并且 排序
        yearList = yearList.stream().distinct().sorted().collect(Collectors.toList());

        StringBuilder result = new StringBuilder();
        int startYear = Integer.parseInt(yearList.get(0));
        int endYear = startYear;

        for (int i = 1; i < yearList.size(); i++) {
            int currentYear = Integer.parseInt(yearList.get(i));

            if (currentYear == endYear + 1) {
                // 连续的年份范围
                endYear = currentYear;
            } else {
                // 非连续的年份范围，输出当前范围
                if (startYear != endYear) {
                    result.append(startYear).append("-").append(endYear).append(" ");
                } else {
                    result.append(startYear).append(" ");
                }

                // 重置起始年份和结束年份
                startYear = endYear = currentYear;
            }
        }

        // 处理最后一个范围
        if (startYear != endYear) {
            result.append(startYear).append("-").append(endYear).append(" ");
        } else {
            result.append(startYear).append(" ");
        }
        if (result.length() > 0) {
            return result.toString();
        }
        return null;
    }

    /**
     * 清洗年份数据
     *
     * @param yearList
     * @param dataVIO
     */
    private static void clenYearData(List<String> yearList, AdsFitmentDataVIO dataVIO) {
        String yearName = dataVIO.getYearName();
        if (ObjectUtils.isEmpty(dataVIO.getYearName())) {
            return;
        }

        // 处理逗号分隔的多个范围
        String[] ranges = yearName.split(",");
        for (String range : ranges) {
            range = range.trim(); // 去除空格
            if (range.contains("-")) {
                String[] parts = range.split("-");
                int startYear = Integer.parseInt(parts[0]);
                int endYear = Integer.parseInt(parts[1]);

                // 将起始年份到结束年份之间的所有年份加入列表
                for (int year = startYear; year <= endYear; year++) {
                    yearList.add(String.valueOf(year));
                }
            } else {
                // 单个年份直接加入列表
                yearList.add(range);
            }
        }
    }

    /**
     * 根据分组的适配数据返回对应的组装数据
     *
     * @param vioList
     * @param i
     * @return
     */
    private String getContent(List<AdsFitmentDataVIO> vioList, int i) {
        // 0: model 英文逗号+空格
        if (Objects.equals(i, 0)) {
            return vioList.stream().map(entry -> entry.getModelName()).collect(Collectors.joining(", "));

        }
        // 1: year+model+liter 英文逗号+空格
        if (Objects.equals(i, 1)) {
            return vioList.stream().map(entry -> Utils.str2Str(entry.getYearName()) + " " + Utils.str2Str(entry.getModelName()) +
                    (ObjectUtils.isNotEmpty(entry.getLiter()) ? " " + entry.getLiter() + "L" : "")).collect(Collectors.joining(", "));

        }
        // 2: year+model+liter 换行
        if (Objects.equals(i, 2)) {
            return vioList.stream().map(entry -> Utils.str2Str(entry.getYearName()) + " " + Utils.str2Str(entry.getModelName()) +
                    (ObjectUtils.isNotEmpty(entry.getLiter()) ? " " + entry.getLiter() + "L" : "")).collect(Collectors.joining("\n"));
        }
        // 3: model+year+liter 英文逗号+空格
        if (Objects.equals(i, 3)) {
            return vioList.stream().map(entry -> Utils.str2Str(entry.getModelName()) + " " + Utils.str2Str(entry.getYearName()) +
                    (ObjectUtils.isNotEmpty(entry.getLiter()) ? " " + entry.getLiter() + "L" : "")).collect(Collectors.joining(", "));
        }
        // 4: model+year+liter 换行
        if (Objects.equals(i, 4)) {
            return vioList.stream().map(entry -> Utils.str2Str(entry.getModelName()) + " " + Utils.str2Str(entry.getYearName()) +
                    (ObjectUtils.isNotEmpty(entry.getLiter()) ? " " + entry.getLiter() + "L" : "")).collect(Collectors.joining("\n"));
        }
        return null;
    }


    public List<FitmentDemand> selectFitmentDemandList(){
        return adsMapper.selectFitmentDemandList();
    }

    @Override
    public List<FitmentDemand> selectFitmentDemandListByType(int type, int offset, int limit) {
        return adsMapper.selectFitmentDemandListByType(type, offset, limit);
    }

    @Override
    public Map<String, Integer> countByPnAndAsinBatch(List<String> asinList) {
        if (CollUtil.isEmpty(asinList)) {
            return new HashMap<>();
        }

        List<Map<String, Object>> results = adsMapper.countByPnAndAsinBatch(asinList);
        Map<String, Integer> countMap = new HashMap<>();

        if (CollUtil.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                String asin = (String) result.get("asin");
                Integer count = ((Number) result.get("count")).intValue();
                countMap.put(asin, count);
            }
        }

        return countMap;
    }

    public void updateFitmentDemandStatus(String status,Long id){
        adsMapper.updateFitmentDemandStatus(status,id);
    }


//    public List<FitExamineDay> getFitExamineDayListByAsins(List<String> asins) {
//        return adsMapper.getFitExamineDayListByAsins(asins);
//    }

    @Override
    public List<Map<String, String>> listDemandByAsins(List<String> notExistsAsin) {
        return adsMapper.listDemandByAsins(notExistsAsin);
    }

    @Override
    public List<AmazonListingChangeMonitorVO> listAmazonListingChangeMonitor(List<String> pdmGoodsCodeList, List<String> asinList, String date) {
        if (CollectionUtil.isEmpty(pdmGoodsCodeList) && CollectionUtil.isEmpty(asinList)) {
            throw new BusinessException("pdmGoodsCodeList和asinList不能同时为空");
        }
        return adsMapper.listAmazonListingChangeMonitor(pdmGoodsCodeList, asinList, date);
    }

    @Override
    public List<AdsFitmentDataBi> getByProduct(String productCode) {
        return adsMapper.getByProduct(productCode);
    }

    @Override
    public List<String> getDataUpdateLinks() {
        return adsMapper.getDataUpdateLinks();
    }

    @Override
    public List<AdsFitmentDataEbayItem> selectFitmentDataEbayItemByProductCode(String productCode) {
        return adsMapper.selectFitmentDataEbayItemByProductCode(productCode);
    }

    @Override
    public List<AdsAmazonListingDataVIO> selectAdsItemFitCompareListBySkuList(List<String> skuList) {
        List<AdsAmazonListingDataVIO> result = new ArrayList<>();
        Lists.partition(skuList, 1000).forEach(list -> {
            List<AdsAmazonListingDataVIO> dataVIOList = adsItemFitCompareMapper.selectAdsItemFitCompareListBySkuList(list);
            if (!CollectionUtils.isEmpty(dataVIOList)) {
                result.addAll(dataVIOList);
            }
        });
        return result;
    }

    @Override
    public List<AdsAmazonListingDataVIO> selectAdsItemFitCompareListByAsinList(List<String> asinList) {
        List<AdsAmazonListingDataVIO> result = new ArrayList<>();
        Lists.partition(asinList, 500).forEach(list -> {
            List<AdsAmazonListingDataVIO> dataVIOList = adsItemFitCompareMapper.selectAdsItemFitCompareListByAsinList(list);
            if (!CollectionUtils.isEmpty(dataVIOList)) {
                result.addAll(dataVIOList);
            }
        });
        return result;
    }

    @Override
    public Integer countCartListByDate(String date, String shopCode, List<String> asinList) {
        return adsMapper.countCartListByDate(date, shopCode, asinList);
    }

    @Override
    public List<AdsFitmentDataOe> selectFitmentDataOeList(List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }
        return adsMapper.selectFitmentDataOeList(productCodes);
    }

    public int countByAsin(String platformGoodsId) {
        return adsMapper.countByAsin(platformGoodsId);
    }
}
