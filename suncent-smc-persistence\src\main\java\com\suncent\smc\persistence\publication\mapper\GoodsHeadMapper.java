package com.suncent.smc.persistence.publication.mapper;

import com.suncent.smc.persistence.aplus.domain.dto.NotRelationAsinDto;
import com.suncent.smc.persistence.aplus.domain.vo.NotRelationAsinVO;
import com.suncent.smc.persistence.publication.domain.dto.HeadPublishUpdateFailGoods;
import com.suncent.smc.persistence.publication.domain.dto.ListingDTO;
import com.suncent.smc.persistence.publication.domain.dto.VCPriceDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.VcLinkErrorDataVO;
import com.suncent.smc.persistence.publication.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SMC-商品主Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-12
 */
public interface GoodsHeadMapper {
    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    GoodsHead selectListingGoodsHeadById(Integer id);

    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    Map<String, String> selectGoodsHeadMapById(@Param("id") Integer id);

    /**
     * 查询店铺编码
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    String selectShopCodeById(Integer id);

    /**
     * 查询SMC-商品主列表
     *
     * @param listingGoodsHead SMC-商品主
     * @return SMC-商品主集合
     */
    List<GoodsHead> selectListingGoodsHeadList(GoodsHead listingGoodsHead);

    /**
     * 查询SMC-商品主列表
     *
     * @param goodsHead SMC-商品主
     * @return SMC-商品主集合
     */
    List<GoodsHeadVO> selectListingGoodsHeadVOList(GoodsHead goodsHead);

    List<GoodsHeadVO> selectListingGoodsHeadVOListCount(GoodsHead goodsHead);

    /**
     * 新增SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    int insertListingGoodsHead(GoodsHead listingGoodsHead);

    /**
     * 修改SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    int updateListingGoodsHead(GoodsHead listingGoodsHead);

    /**
     * 删除SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return 结果
     */
    int deleteListingGoodsHeadById(Integer id);

    /**
     * 批量删除SMC-商品主
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteListingGoodsHeadByIds(Integer[] ids);

    /**
     * 批量删除SMC-商品主表数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteListingGoodsHeadByIdList(@Param("ids") Integer[] ids);

    /**
     * 批量下架Listing
     *
     * @param ids 需要下架的Listing主键
     * @return 结果
     */
    int stopPublishListingByIds(@Param("ids") Integer[] ids);

    /**
     * 批量刊登Listing
     *
     * @param ids 需要刊登的Listing主键
     * @return 结果
     */
    int publishListingByIds(@Param("ids") Integer[] ids);

    /**
     * 查询商品头部基础数据集合
     *
     * @param ids 需要查询的Listing主键
     * @return SMC-商品集合
     */
    List<GoodsHead> selectListingGoodsHeadByIds(@Param("ids") Integer[] ids);

    /**
     * 批量新增SMC-商品主
     *
     * @param goodsHeadList SMC-商品主
     * @return 结果
     */
    int insertListingGoodsHeads(@Param("goodsHeadList") List<GoodsHead> goodsHeadList);


    /**
     * 获取AM没有asin的商品
     * @return
     */
    List<GoodsHead> selectAmGoodsHeads(GoodsHead goodsHead);


    /**
     * 根据商品编码查询待更新库存的商品
     * @return
     */
    List<GoodsHead> selectListingByPdmGoodsCode(@Param("codes") String[] code);
    /**
     * 更新库存为0 状态为更新中
     * 针对在线商品
     * @return
     */
    int updateListingByIds(@Param("ids") String[] ids);

    /**
     * 更新刊登状态为更新中
     * @param ids
     * @return
     */
    int updateListingPublishStatusByIds(@Param("ids")Integer[] ids);
    /**
     * 查询Listing主表
     *
     * @param goodsHead
     * @return
     * @Date 2023/6/15 16:38
     * <AUTHOR>
     */
    List<GoodsHeadVO> getListingGoodsHeadVOList(GoodsHead goodsHead);
    /**
     * 通过标题查询Listing主表
     * @param goodsHead
     * @return
     */

    Integer selectCountByTitle(GoodsHead goodsHead);

    List<GoodsHeadHomePageVO> getGroundingListing(GoodsHeadHomePageVO goodsHeadHomePageVO);

    List<GoodsHeadHomePageVO> getSalesingListing(GoodsHeadHomePageVO goodsHeadHomePageVO);

    List<GoodsHeadHomePageVO> getOffListing(GoodsHeadHomePageVO goodsHeadHomePageVO);
    /**
     * 查询当前店铺 该sku 自动刊登有多少listing
     * @param queryExist
     * @return
     */
    Integer selectCountByAutoSku(GoodsHead queryExist);

    int updateListingGoodsHeadByIdList(GoodsHead goodsHead);

    List<GoodsHead> selectOnlineListingByPlatfromAndShopCode(@Param("platform") String platform, @Param("shopCode") String shopCode);

    List<GoodsHead> selectListBySkuList(GoodsHead goodsHead);

    List<String> selectGoodsCodeSale(@Param("userName")String userName);

    List<String> selectAllGoodsCode();

    int updateListingRemarkByIds(@Param("remark")String remark, @Param("ids")Integer[] ids);

    List<GoodsHeadVO> listingGoodsHeadVOList(@Param("ids") Integer[] ids);

    List<Integer> selectSaleAndOrderList(ListingDTO listingDTO);

    int clearPlatformGoodId(GoodsHead goodsHead);

    List<GoodsHead> selectNewOnlineTimeByShopCodeAndSize(@Param("platformItemIds") List<String> platformItemIds, @Param("shopCode") String shopCode, @Param("size") Long size);


    List<GoodsHead> selectHeadByShopCodeAndShopCategory( @Param("shopCode") String shopCode, @Param("shopCategory") String shopCategory);

    List<GoodsHead> selectFailListing();

    int deleteFailListing();

    List<GoodsHead> selectHeadListByShopAndTitle(GoodsHead listingGoodsHead);

    List<GoodsHead> selectHeadsByPublishStatusAndUpdateTime(HeadPublishUpdateFailGoods dto);

    GoodsHead queryLatestListing(@Param("skuList") List<String> skuList);

    List<GoodsHead> selectListByPlatformGoodsCodes(@Param("platformGoodsCodes") List<String> platformGoodsCodes);

    List<GoodsHead> selectListInventoryNoZeroBySkuList(GoodsHead query);

    List<GoodsHeadVO> selectListingGoodsByShopAndSku(@Param("shopCodeList")List<String> shopCodeList, @Param("skuList")List<String> skuList,@Param("userId")Long userId);

    List<String> selectGoodsCodeSaleByShop(@Param("shopCodeList")List<String> shopCodeList,@Param("userId")Long userId);

    List<GoodsHeadVO> selectListingUpdateFailureByShop(@Param("shopCodeList")List<String> shopCodeList,@Param("userId")Long userId);

    List<GoodsHead> selectOnlineListing(@Param("shopCode") String shopCode, @Param("skus")List<String> skus);


    List<GoodsHead> selectListingZeroQty(@Param("platform") String platform,@Param("siteCode") String siteCode, @Param("shopCode")String shopCode);

    List<NotRelationAsinVO> selectNotRelationAPlusListing(NotRelationAsinDto notRelationAsinDto);

    int updataAsinRefundRateLabel(@Param("platform") String platform, @Param("platformSaleCode") String platformSaleCode, @Param("refundRateLabel") String refundRateLabel);

    int updataSkuRefundRateLabel(@Param("platform")String platform, @Param("sku")String sku, @Param("refundRateLabel") String refundRateLabel);

    List<GoodsHead> selectFailTodoListing(GoodsHead goodsHead);

    Integer countByPublishFailByShopCodes(@Param("shopCodes") List<String> shopCodes);

    List<String> selectAsinByShopCodeAndAsinIsNotNull(String shopCode);

    int updateByAMReport(@Param("quantity") String quantity,@Param("price") String price, @Param("publishStatus")Integer publishStatus, @Param("sellerSku")String sellerSku,@Param("asin") String asin,@Param("shopCode")String shopCode);

    List<GoodsHeadVO> selectListingGoodsByShopCodesAndSku(@Param("shopCodes") List<String> shopCodes, @Param("platformSku") String platformSku, @Param("goodsCode") String goodsCode);

    Integer countListingGoodsByShopCodesAndSku(@Param("shopCodes") List<String> shopCodes, @Param("platformSku") String platformSku, @Param("goodsCode") String goodsCode);

    Integer batchUpdateSettlementPrice(List<GoodsHead> goodsHeadList);
    Integer batchUpdateSettlementPriceV2();

    Integer batchUpdateRedLinePrice(List<GoodsHead> goodsHeadList);

    Integer batchUpdateRedlinePriceV2(List<GoodsHead> goodsHeadList);

    Integer updateRedLinePrice(@Param("redLinePrice")BigDecimal redLinePrice, @Param("publishType")Integer publishType,@Param("pdmGoodsCode")String pdmGoodsCode);

    List<GoodsHead> selectPriceTodoListing();
    List<GoodsHeadPriceVo> getGoodsHeadPriceVoList(List<String> goodsCodeList);

    int clearOriginalPrice(GoodsHead goodsHead);

    List<Integer> selectListIds(GoodsHead listingGoodsHead);

    List<GoodsHeadVO> countGoodsHeadVOList(GoodsHead goodsHeadParam);

    int updateOperators(@Param("operators") String operators, @Param("sitCode")String sitCode, @Param("shopCode")String shopCode, @Param("platformSku")String platformSku, @Param("goodsCode")String goodsCode);

    List<GoodsHeadCountVO> countOnlineListing(@Param("skus") List<String> skus);

    int createTempTableAndInsertData();

    int clearAMainTable();

    int updateMainTable();


    int dropTempTable();

    List<GoodsHeadVO> selectListingByShopCode(@Param("shopList")List<String> shopList);

    int selectListingCount(@Param("goodsId")List<Integer> goodsId);

    List<GoodsHead> selectAmListingByPlatformGoodsIdList(@Param("asinList") List<String> asinList);

    int updateZeroInventory(@Param("goodsId")List<Integer> goodsId);

    List<GoodsHeadCountVO> countOnlineListingGroup(List<String> goodsCodes);

    List<GoodsHead> selectAMOnlineListingByAsinAndPlatformSku(@Param("asin") String asin, @Param("platformSku") String platformSku, @Param("shopCode") String shopCode, @Param("siteCode") String siteCode);

    int updateHeadsAdapterByItemId(@Param("goodsHeads")List<GoodsHead> goodsHeads);

    List<GoodsHead> selectOnlineListingByAsinAndPlatformSku(@Param("asin") String asin, @Param("platformSku") String platformSku, @Param("shopCode") String shopCode, @Param("siteCode") String siteCode, @Param("platformCode") String platformCode);

    List<GoodsHead> selectAllListing(@Param("shopCode") String shopCode, @Param("skus") List<String> skus);

    List<GoodsHead> listNeedUpdateItemsPerInnerPack(@Param("publishType") Integer publishType, @Param("platformSkus") List<String> platformSkus, @Param("lastId") Integer lastId);

    List<GoodsHead> listNeedSyncRealTimeSales(@Param("platformGoodsCodes") List<String> platformGoodsCodes);

    List<GoodsHead> selectVCDFListing(@Param("sellerSkuList") List<String> sellerSkuList);

    int updateVcInventoryBySellerSku(@Param("sellerSkuLit") List<String> sellerSkuLit);

    List<GoodsHead> selectVCDFListingBySku(@Param("skuList") List<String> skuList, @Param("shopCode") String shopCode);

    List<GoodsHead> listFailListing(String userId);


    List<String> listFollowSoldAsin(@Param("publishType") String publishType, @Param("goodsId") List<String> goodsId);


    List<GoodsHead> selectDeleteListingGoodsHeadList(@Param("sellerSku") String sellerSku, @Param("shopCode") String shopCode, @Param("publishType") Integer publishType, @Param("asin") String asin, @Param("day") Integer day);

    List<GoodsHead> listWaitAdaptByLastId(Integer lastId);

    List<GoodsHead> listNeedSyncSeriesByLastId(Integer lastId);

    GoodsHead selectLastGoodsHeadByAsin(@Param("asin") String asin, @Param("id") Integer id);

    List<GoodsHead> listNeedUpdatePublishStatus(Integer lastId);

    int countByPlatformGoodsCodeAndShopCodeAndPublishType(@Param("platformSku") String platformSku, @Param("shopCode") String shopCode, @Param("publishType") Integer publishType, @Param("id") Integer id);

    List<GoodsHead> selectAMList(@Param("createBy") String createBy, @Param("shopCode") String shopCode, @Param("categoryId") String categoryId);

    List<GoodsHead> selectGoodsHeadByShopCodeAndAsin(@Param("shopCode") String shopCode, @Param("asinList") List<String> asinList);

    List<GoodsHead> selectScAsinList(@Param("minId") Integer minId, @Param("maxId") Integer maxId);

    Integer selectMaxIdBefore270Days();

    void insertScAsinList(@Param("asinList") List<String> asinList);

    List<String> listTempAsinList();

    List<GoodsHead> selectGoodsHeadByAsin(@Param("asinList") List<String> asinList, @Param("platform") String platform);

    void deleteTempAsinList(@Param("asinList") List<String> asinList);

    void deleteTempAsinListAll();

    Long getNeedBackupStartId();

    List<GoodsHead> listNeedBackup(@Param("lastId") Long lastId , @Param("maxId") Long maxId);

    List<GoodsHeadCountVO> listMainBrandListing(@Param("goodsCodeList") List<String> goodsCodeList, @Param("mainBrandList") List<String> mainBrandList);

    List<GoodsHead> selectAMListingByBrand(@Param("brandCode") String brandCode, @Param("lastId") Long lastId, @Param("beforeDate") Date beforeDate);


    List<GoodsHead> selectGoodsHeadByShopCodeAndPlatformGoodsCode(@Param("shopCode") String shopCode,@Param("publishType") String publishType, @Param("platformSkus") List<String> platformSkus);

    List<VcLinkErrorDataVO> listVcErrorData(@Param("userId") Long userId, @Param("period") String period);

    List<GoodsHead> queryNeedPullEbayListings();


    List<GoodsHead> queryNeedPullAmazonListings();

    void updateListingOffSale(@Param("itemId") String itemId, @Param("accountCode") String accountCode, @Param("publishStatus") Integer publishStatus);

    List<GoodsHead> listByPlatformAndShopCode(@Param("platform") String platform, @Param("shopCode") String shopCode, @Param("platformSkuList") List<String> platformSkuList);

    List<VCPriceVO> queryMonitorPoolVCPrice(@Param("vcPriceDTOS") List<VCPriceDTO> vcPriceDTOS);

    /**
     * 根据ASIN获取品牌信息
     * 
     * @param asin ASIN
     * @return 品牌编码
     */
    String selectBrandByAsin(@Param("asin") String asin);

    // 红线价白名单 清空临时表
    void truncateTempRedLineWhiteSkus();

    // 红线价白名单 插入临时表
    void insertTempRedLineWhiteSkus(@Param("pdmGoodsCodes") List<String> pdmGoodsCodes);


    /**
     * 根据ASIN查询其他链接
     *
     * @param asin ASIN值
     * @param currentHeadId 当前商品ID（排除此ID）
     * @return 其他链接信息列表
     */
    List<GoodsHead> selectOtherListingsByAsin(@Param("asin") String asin, @Param("currentHeadId") Long currentHeadId);

    void clearTempAsinSalesTable();

    void insertAsinSales(@Param("asinList") List<String> asinList);

    List<String> listAsinSales(int batchSize);

    /**
     * 物理删除临时表中已处理的ASIN列表
     * @param asinList ASIN列表
     */
    void deleteAsinSales(@Param("asinList") List<String> asinList);

    String selectListingGoodsHeadByAsinAndShopCode(@Param("asin") String asin, @Param("shopCode") String shopCode, @Param("sku") String sku, @Param("publishType") Integer publishType);
}