package com.suncent.smc.quartz.task.listing;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.enums.PublishStatus;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.ads.domain.AdsListingLabel;
import com.suncent.smc.persistence.ads.domain.AdsRecordData;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.bi.domain.entity.DwiPromotionsDetail;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.entity.CrlRpaAdvertisingRestrictions;
import com.suncent.smc.persistence.bi.service.IBi2DataService;
import com.suncent.smc.persistence.bi.service.IBiDataService;
import com.suncent.smc.persistence.bi.service.IOdsCrlCrlVcCatalogDataService;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.competitiveProducts.domain.ItemTempDay;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IListingLabelService;
import com.suncent.smc.provider.biz.publication.DingdingMonitorInfoBiz;
import com.suncent.smc.provider.biz.publication.ListingInfoBiz;
import com.suncent.smc.system.service.ISysConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.suncent.smc.common.enums.MonitorEnum.ARRAY_GROUP_BD_DATA_NOT_GENERATED;

/**
 * listing标签处理定时任务 ->生成smc listing-label关系数据
 * BI业务综合分析同步任务兼容ads数据组标签同步任务
 *
 *  ListingLabelTask 晚于 LostCartTask执行
 */
@Component
@Slf4j
public class ListingLabelTask {
    @Autowired
    IBiDataService biDataService;
    @Autowired
    IBi2DataService bi2DataService;
    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    IAdsService adsService;
    @Autowired
    IListingLabelService listingLabelService;
    @Autowired
    ISysConfigService sysConfigService;
    @Autowired
    IShopService shopService;
    @Autowired
    protected ThreadPoolForMonitorManager threadPoolForMonitorManager;

    @Autowired
    private ListingInfoBiz listingInfoBiz;

    @Autowired
    @Qualifier("mongoTemplateAWS")
    private MongoTemplate awsMongoTemplate;

    @Autowired
    private DingdingMonitorInfoBiz dingdingMonitorInfoBiz;

    @Autowired
    private IOdsCrlCrlVcCatalogDataService odsCrlCrlVcCatalogDataService;

    /**
     * listing标签处理定时任务
     */
    @XxlJob("listingLabelTask")
    public void listingLabelTask() {
        log.info("listing标签处理定时任务执行开始");
        try {
            List<Shop> shopList = shopService.selectShopList();
            if (ObjUtil.isEmpty(shopList)) {
                log.error("listing标签处理定时任务-处理数据为空,请检查 pdm shop表数据是否正常");
                return;
            }
            ThreadPoolExecutor listingUpdatePool = threadPoolForMonitorManager.getThreadPoolExecutor("listingLabelTaskPool");


            for (Shop shop : shopList) {
                String shopCode = shop.getShopCode();
                if (ObjUtil.isEmpty(shopCode)) {
                    log.error("listing标签处理定时任务-shopCode数据为空,请检查 pdm shop表数据是否正常");
                    continue;
                }

                listingUpdatePool.execute(() -> {
                    dohandle(shopCode);
                });
            }

        } catch (Exception e) {
            log.error("listing标签处理定时任务执行异常", e);
        } finally {
            long end3 = System.currentTimeMillis();
            XxlJobHelper.log("listing标签定时任务处理主链接数据执行开始");
            updateGoodHeadMain();
            long end4 = System.currentTimeMillis();
            XxlJobHelper.log("listing标签定时任务处理主链接数据执行完成,耗时：" + (end4 - end3) + "ms");
        }
        log.info("listing标签处理定时任务任务表执行结束");
    }

    /**
     * 按照店铺处理标签数据
     *
     * @param shopCode
     */
    private void dohandle(String shopCode) {

        XxlJobHelper.log("listing标签定时任务处理BI数据执行开始");
        long start = System.currentTimeMillis();
        syncBiData(shopCode);
        long end1 = System.currentTimeMillis();
        XxlJobHelper.log("listing标签定时任务处理BI数据执行完成,耗时：" + (end1 - start) + "ms");

        XxlJobHelper.log("listing标签定时任务处理ADS数据执行开始");
        long end2 = System.currentTimeMillis();
        syncAdsData(shopCode);
        long end3 = System.currentTimeMillis();
        XxlJobHelper.log("listing标签定时任务处理ADS数据执行完成,耗时：" + (end3 - end2) + "ms");

        // VC同步状态，只有VC1根据是否有购物车变为在售和非在售状态
        if ("VC1".equalsIgnoreCase(shopCode)) {
//            syncVCPublishStatus();
            processItemTempDayLabels();
        }
    }
    @XxlJob("syncVCPublishStatus")
    public void syncVCPublishStatusJob() {
        syncVCPublishStatus();
    }

    public void syncVCPublishStatus() {
        log.info("同步VC1的商品状态开始");
        int allSize = 0;

        try {
            // 通过it_demand表查询对应记录的状态是否变成非0，如果非0就标记为处理中
            int lastId = 0;

            List<GoodsHead> needUpdateList = goodsHeadService.listNeedUpdatePublishStatus(lastId);
            if (CollUtil.isEmpty(needUpdateList)) {
                return;
            }
            allSize = needUpdateList.size();
            lastId = needUpdateList.stream().map(GoodsHead::getId).max(Integer::compareTo).orElse(0);
            if (lastId == 0) {
                return;
            }
            listingInfoBiz.handlerUpdate(needUpdateList);
            while (CollUtil.isNotEmpty(needUpdateList)) {
                needUpdateList = goodsHeadService.listNeedUpdatePublishStatus(lastId);
                if (CollUtil.isEmpty(needUpdateList)) {
                    break;
                }
                lastId = needUpdateList.stream().map(GoodsHead::getId).max(Integer::compareTo).orElse(0);
                if (lastId == 0) {
                    break;
                }
                allSize += needUpdateList.size();
                listingInfoBiz.handlerUpdate(needUpdateList);
            }
        }catch (Exception e){
            log.error("同步VC1的商品状态异常",e);
        }

        log.info("同步VC1的商品状态结束，共处理{}条数据", allSize);
    }



    /**
     * 更新商品主表 主链接数据
     */
    public void updateGoodHeadMain() {
        goodsHeadService.updateGoodHeadMain();
    }

    /**
     * 获取ads数据 同步更新链接标签信息
     */
    public void syncAdsData(String shopCode) {
        try {
            // 获取ads标签数据和当天购物车数据
            List<AdsListingLabel> adsLabelList = adsService.getListingLabelList(shopCode);
            String currentDate = DateUtils.getDate();
            List<AdsRecordData> todayCartList = adsService.selectTodayCartList(currentDate, shopCode,null);

            // 如果标签和购物车数据都为空，记录错误日志并退出
            if (ObjUtil.isEmpty(adsLabelList) && ObjUtil.isEmpty(todayCartList)) {
                log.error("listing标签处理定时任务-处理店铺：{}, ads数据组标签无数据, 请检查ads数据是否正常", shopCode);
                return;
            }

            // 设置标签数据
            setListingLabelAds(adsLabelList, todayCartList, shopCode);
            List<ListingLabel> dbLabelList = new ArrayList<>();

            // 处理标签列表
            for (AdsListingLabel label : adsLabelList) {
                if (ObjUtil.isNotEmpty(label.getHeadId())) {
                    addLabel(dbLabelList, label.getHeadId(), label.getPlatformCode(), label.getSitCode(), shopCode, "ads", label.getPriceLabel(), DateUtils.getNowDate());
                    if (ObjUtil.isNotEmpty(label.getListingLabel())){
                        addLabel(dbLabelList, label.getHeadId(), label.getPlatformCode(), label.getSitCode(), shopCode, "ads", "链接质量" + label.getListingLabel(), DateUtils.getNowDate());
                    }
                    addLabels(dbLabelList, label.getHeadId(), label.getPlatformCode(), label.getSitCode(), shopCode, "ads", label.getAdsPerformanceLabel(), DateUtils.getNowDate());
                    addLabels(dbLabelList, label.getHeadId(), label.getPlatformCode(), label.getSitCode(), shopCode, "ads", label.getVcLabel(), DateUtils.getNowDate());
                    addLabel(dbLabelList, label.getHeadId(), label.getPlatformCode(), label.getSitCode(), shopCode, "ads", label.getCartLabel(), DateUtils.getNowDate());
                }
            }

            // 去重并过滤无效标签
            dbLabelList = filterAndDeduplicateLabels(dbLabelList);

            if (ObjUtil.isNotEmpty(dbLabelList)) {
                // 删除旧标签并插入新标签
                List<String> label = dbLabelList.stream().map(ListingLabel::getLabel).distinct().collect(Collectors.toList());
                if (ObjUtil.isNotEmpty(label)) {
                    listingLabelService.deleteListingLabelsByShopCode(label, shopCode);
                    Lists.partition(dbLabelList, 1000).forEach(listingLabelService::insertListingLabelBatch);
                }
            }
        } catch (Exception e) {
            log.error("listing标签处理定时任务-处理ads数据组标签出错", e);
        }
    }

    /**
     * 转换单个标签数据
     * @param dbLabelList
     * @param headId
     * @param platform
     * @param siteCode
     * @param shopCode
     * @param labelType
     * @param label
     * @param createTime
     */
    private void addLabel(List<ListingLabel> dbLabelList, Integer headId, String platform, String siteCode, String shopCode, String labelType, String label, Date createTime) {
        if (ObjUtil.isNotEmpty(label)) {
            ListingLabel listingLabel = new ListingLabel();
            listingLabel.setHeadId(headId);
            listingLabel.setPlatform(platform);
            listingLabel.setSiteCode(siteCode);
            listingLabel.setShopCode(shopCode);
            listingLabel.setLabelType(labelType);
            listingLabel.setLabel(label);
            listingLabel.setCreateTime(createTime);
            dbLabelList.add(listingLabel);
        }
    }

    /**
     * 转换多个标签数据
     * @param dbLabelList
     * @param headId
     * @param platform
     * @param siteCode
     * @param shopCode
     * @param labelType
     * @param labels
     * @param createTime
     */
    private void addLabels(List<ListingLabel> dbLabelList, Integer headId, String platform, String siteCode, String shopCode, String labelType, String labels, Date createTime) {
        if (ObjUtil.isNotEmpty(labels)) {
            for (String label : labels.split("/")) {
                addLabel(dbLabelList, headId, platform, siteCode, shopCode, labelType, label, createTime);
            }
        }
    }

    /**
     * 过滤label为空的数据 并且 标签+id 唯一
     * @param dbLabelList
     * @return
     */
    private List<ListingLabel> filterAndDeduplicateLabels(List<ListingLabel> dbLabelList) {
        return new ArrayList<>(dbLabelList.stream()
                .filter(label -> ObjUtil.isNotEmpty(label.getLabel()))
                .collect(Collectors.toMap(
                        label -> label.getHeadId() + "_" + label.getLabel(),
                        label -> label,
                        (existing, replacement) -> existing
                ))
                .values());
    }


    /**
     * 找的ads数据在smc对应的链接
     *
     * @param listingLabelList
     * @param adsRecordData
     */
    private void setListingLabelAds(List<AdsListingLabel> listingLabelList, List<AdsRecordData> adsRecordData, String shopCode) {
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingByShopCode(Collections.singletonList(shopCode));
        if (ObjUtil.isEmpty(goodsHeadVOList)) {
            log.error("listing标签处理定时任务-处理店铺:{},ads数据组标签无对应的smc商品主数据,请检查smc数据是否正常",shopCode);
            return;
        }
        HashMap<String,Integer> goodsHeadVOMap = goodsHeadVOList.stream()
                .collect(Collectors.toMap(goodsHeadVO ->
                                goodsHeadVO.getShopCode() + goodsHeadVO.getPdmGoodsCode() + goodsHeadVO.getPlatformGoodsCode() + goodsHeadVO.getPlatformGoodsId(),
                        GoodsHeadVO::getId, (k1, k2) -> k1, HashMap::new));
        Map<String, Integer> cartMap = new HashMap<>();

        Map<String, List<GoodsHeadVO>> asinListPriceMap=new HashMap<>();
        //购物车标签 只取在售链接  按照shop+asin 分组 取当前售价最小的id,如果多条取有库存的,仍存在多条，取上架时间最早的id
        if ("VC1".equals(shopCode)) {
            cartMap = goodsHeadVOList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getShopCode() + vo.getPlatformGoodsId(),
                            Collectors.collectingAndThen(
                                    Collectors.minBy(Comparator.comparing(GoodsHeadVO::getStandardPrice)
                                            .thenComparing(Comparator.comparing(GoodsHeadVO::getStockOnSalesQty, Comparator.nullsLast(Comparator.naturalOrder()))
                                                    .thenComparing(GoodsHeadVO::getOnlineTime, Comparator.nullsLast(Comparator.naturalOrder())))),
                                    optional -> optional.map(GoodsHeadVO::getId).orElse(null)
                            )
                    ));

            //处理页面价格高于list_price
            Map<String, List<GoodsHeadVO>> asinListPriceMapVC = goodsHeadVOList.stream()
                    .filter(f -> ObjUtil.isNotEmpty(f.getPlatformGoodsId()))
                    .collect(Collectors.groupingBy(e -> e.getPlatformGoodsId()));
            asinListPriceMap.putAll(asinListPriceMapVC);
        } else {
            cartMap = goodsHeadVOList.stream().filter(v -> PublishStatus.getSaleStatus().contains(v.getPublishStatus()))
                    .collect(Collectors.groupingBy(
                            vo -> vo.getShopCode() + vo.getPlatformGoodsId(),
                            Collectors.collectingAndThen(
                                    Collectors.minBy(Comparator.comparing(GoodsHeadVO::getStandardPrice)
                                            .thenComparing(Comparator.comparing(GoodsHeadVO::getStockOnSalesQty, Comparator.nullsLast(Comparator.naturalOrder()))
                                                    .thenComparing(GoodsHeadVO::getOnlineTime, Comparator.nullsLast(Comparator.naturalOrder())))),
                                    optional -> optional.map(GoodsHeadVO::getId).orElse(null)
                            )
                    ));
        }



        //处理标签指标数据
        listingLabelList.forEach(label -> {
            Integer id = goodsHeadVOMap.get(label.getShopCode() + label.getSku() + label.getPlatformSku() + label.getAsin());
            if (ObjUtil.isNotEmpty(id)) {
                label.setHeadId(id);
            }
        });
        //处理购物车数据
        Map<String, Integer> finalCartMap = cartMap;
        adsRecordData.forEach(data -> {
            Integer goodId = finalCartMap.get(data.getShopCode() + data.getPageAsin());
            if (ObjUtil.isNotEmpty(goodId) && ObjUtil.equals("1",data.getCart())) {
                AdsListingLabel adsListingLabel = new AdsListingLabel();
                adsListingLabel.setHeadId(goodId);
                adsListingLabel.setPlatformCode(PlatformTypeEnum.AM.name());
                adsListingLabel.setSitCode(ObjUtil.isNotEmpty(data.getCountry()) ? data.getCountry().toUpperCase() : "US");
                adsListingLabel.setShopCode(data.getShopCode());
                adsListingLabel.setCartLabel("有购物车");
                listingLabelList.add(adsListingLabel);
            }

            List<GoodsHeadVO> goodsHeadVOS = asinListPriceMap.get(data.getPageAsin());
            if (CollUtil.isNotEmpty(goodsHeadVOS)){
                // 检查该ASIN是否在最近3个月有销量
                boolean hasSalesInLast3Months = checkAsinSalesInLast3Months(data.getPageAsin());
                if (hasSalesInLast3Months) {
                    for (GoodsHeadVO goodsHeadVO : goodsHeadVOS) {
                        AdsListingLabel adsListingLabel = new AdsListingLabel();
                        adsListingLabel.setHeadId(goodsHeadVO.getId());
                        adsListingLabel.setPlatformCode(PlatformTypeEnum.AM.name());
                        adsListingLabel.setSitCode(ObjUtil.isNotEmpty(data.getCountry()) ? data.getCountry().toUpperCase() : "US");
                        adsListingLabel.setShopCode(data.getShopCode());
                        adsListingLabel.setCartLabel("LP异常");
                        listingLabelList.add(adsListingLabel);
                    }
                }
            }
        });

    }

    /**
     * 检查ASIN在最近3个月是否有销量
     * @param asin ASIN编码
     * @return true-有销量，false-无销量
     */
    private boolean checkAsinSalesInLast3Months(String asin) {
        try {
            if (StrUtil.isBlank(asin)) {
                return false;
            }

            // 查询最近3个月的销量数据
            List<Map<String, String>> salesData = odsCrlCrlVcCatalogDataService.listAutoSalesDetailByDate(
                Collections.singletonList(asin), 3);

            if (CollUtil.isEmpty(salesData)) {
                return false;
            }

            // 检查是否有销量大于0的记录
            return salesData.stream().anyMatch(map -> Convert.toBigDecimal(map.get("sales_volume"), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0);
        } catch (Exception e) {
            log.error("检查ASIN[{}]最近3个月销量失败", asin, e);
            // 出现异常时，为了保险起见，返回false，不生成LP异常标签
            return true;
        }
    }

    /**
     * 同步bi数据、入bi库
     */
    private void syncBiData(String shopCode) {
        try {
            List<BiSalesAnalysis> analysisList = biDataService.queryTodaySalesAnalysis(shopCode);
            if (ObjUtil.isEmpty(analysisList)) {
                log.error("listing标签处理定时任务-处理店铺:{},BI业务综合分析同步任务表今日无数据,请检查BI数据是否正常",shopCode);
                return;
            }
            //获取头表id
            setListingLabelBi(analysisList, shopCode);
            List<ListingLabel> dbList = new ArrayList<>();
            String salesQty7Qoq = sysConfigService.selectConfigByKey("sales_qty_7_qoq");
            if (ObjUtil.isEmpty(salesQty7Qoq)) {
                salesQty7Qoq = "0.1";
            }
            String sessions7Qoq = sysConfigService.selectConfigByKey("sessions_7_qoq");
            if (ObjUtil.isEmpty(sessions7Qoq)) {
                sessions7Qoq = "0.1";
            }
            String adSpend7Qoq = sysConfigService.selectConfigByKey("ad_spend_7_qoq");
            if (ObjUtil.isEmpty(adSpend7Qoq)) {
                adSpend7Qoq = "0.1";
            }
            String grossProfitRealRate7Qoq = sysConfigService.selectConfigByKey("gross_profit_real_rate_7_qoq");
            if (ObjUtil.isEmpty(grossProfitRealRate7Qoq)) {
                grossProfitRealRate7Qoq = "0.1";
            }


            String finalSalesQty7Qoq = salesQty7Qoq;
            String finalSessions7Qoq = sessions7Qoq;
            String finalAdSpend7Qoq = adSpend7Qoq;
            String finalGrossProfitRealRate7Qoq = grossProfitRealRate7Qoq;
            List<ListingLabel> finalDbList = dbList;
            analysisList.stream().filter(l -> ObjUtil.isNotEmpty(l.getHeadId())).forEach(analysis -> {
                //销量标签-> sales_qty_7_qoq

                if (ObjUtil.isNotEmpty(analysis.getSalesQty7Qoq())) {
                    ListingLabel listingLabel = new ListingLabel();
                    listingLabel.setHeadId(analysis.getHeadId());
                    listingLabel.setPlatform(Objects.equals("AMAZON", analysis.getPlatform()) ? "AM" : "EB");
                    listingLabel.setSiteCode(analysis.getCountry());
                    listingLabel.setShopCode(analysis.getShopCode());
                    listingLabel.setLabelType("bi");
                    listingLabel.setLabel(getBiLabel("销量", analysis.getSalesQty7Qoq(), finalSalesQty7Qoq));
                    listingLabel.setCreateTime(DateUtils.getNowDate());
                    finalDbList.add(listingLabel);

                }

                //流量标签-> sessions_7_qoq
                if (ObjUtil.isNotEmpty(analysis.getSessions7Qoq())) {
                    ListingLabel listingLabel = new ListingLabel();
                    listingLabel.setHeadId(analysis.getHeadId());
                    listingLabel.setPlatform(Objects.equals("AMAZON", analysis.getPlatform()) ? "AM" : "EB");
                    listingLabel.setSiteCode(analysis.getCountry());
                    listingLabel.setShopCode(analysis.getShopCode());
                    listingLabel.setLabelType("bi");
                    listingLabel.setLabel(getBiLabel("流量", analysis.getSessions7Qoq(), finalSessions7Qoq));
                    listingLabel.setCreateTime(DateUtils.getNowDate());
                    finalDbList.add(listingLabel);

                }

                //广告费标签-> ad_spend_7_qoq
                if (ObjUtil.isNotEmpty(analysis.getAdSpend7Qoq())) {
                    ListingLabel listingLabel = new ListingLabel();
                    listingLabel.setHeadId(analysis.getHeadId());
                    listingLabel.setPlatform(Objects.equals("AMAZON", analysis.getPlatform()) ? "AM" : "EB");
                    listingLabel.setSiteCode(analysis.getCountry());
                    listingLabel.setShopCode(analysis.getShopCode());
                    listingLabel.setLabelType("bi");
                    listingLabel.setLabel(getBiLabel("广告费", analysis.getAdSpend7Qoq(), finalAdSpend7Qoq));
                    listingLabel.setCreateTime(DateUtils.getNowDate());
                    finalDbList.add(listingLabel);

                }

                //毛利标签-> gross_profit_real_rate_7_qoq
                if (ObjUtil.isNotEmpty(analysis.getGrossProfitRealRate7Qoq())) {
                    ListingLabel listingLabel = new ListingLabel();
                    listingLabel.setHeadId(analysis.getHeadId());
                    listingLabel.setPlatform(Objects.equals("AMAZON", analysis.getPlatform()) ? "AM" : "EB");
                    listingLabel.setSiteCode(analysis.getCountry());
                    listingLabel.setShopCode(analysis.getShopCode());
                    listingLabel.setLabelType("bi");
                    listingLabel.setLabel(getBiLabel("毛利", analysis.getGrossProfitRealRate7Qoq(), finalGrossProfitRealRate7Qoq));
                    listingLabel.setCreateTime(DateUtils.getNowDate());
                    finalDbList.add(listingLabel);

                }


            });
            // 过滤label为空的数据 并且 标签+id 唯一
            dbList = finalDbList.stream().filter(l -> ObjUtil.isNotEmpty(l.getLabel()))
                    .collect(Collectors.toMap(
                            item -> item.getHeadId() + "_" + item.getLabel(),
                            item -> item,
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            if (ObjUtil.isNotEmpty(dbList)) {
                listingLabelService.deleteListingLabelByShopCode("bi", shopCode);
                Lists.partition(dbList, 1000).forEach(list -> {
                    listingLabelService.insertListingLabelBatch(list);
                });
            }
        } catch (Exception e) {
            log.error("ListingLabelTask bi标签数据同步失败", e);
        }


    }

    /**
     * 获取标签
     *
     * @param type     类型
     * @param current  当前值
     * @param standard 基准值
     * @return
     */
    private String getBiLabel(String type, String current, String standard) {
        //current与standard 对比大小 小就返回空,大还需要看current是正数就暴涨 是负数就暴跌
        if (Double.parseDouble(current) < Double.parseDouble(standard)) {
            return null;
        }
        if (current.startsWith("-")) {
            return type + "暴跌";
        }
        return type + "暴涨";
    }

    /**
     * 找的ads数据在smc对应的链接
     *
     * @param analysisList
     */
    private void setListingLabelBi(List<BiSalesAnalysis> analysisList, String shopCode) {
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingGoodsByShopCodeAndSku(Collections.singletonList(shopCode), null, null);
        if (ObjUtil.isEmpty(goodsHeadVOList)) {
            log.error("listing标签处理定时任务-处理店铺:{},BI业务综合分析同步任务表无对应的smc商品主数据,请检查smc数据是否正常",shopCode);
            return;
        }
        HashMap<String, Integer> goodsHeadVOMap = goodsHeadVOList.stream()
                .collect(Collectors.toMap(goodsHeadVO ->
                                goodsHeadVO.getShopCode() + goodsHeadVO.getPlatformGoodsCode() + goodsHeadVO.getPlatformGoodsId(),
                        GoodsHeadVO::getId, (k1, k2) -> k1, HashMap::new));

        analysisList.forEach(analysis -> {
            Integer id = goodsHeadVOMap.get(analysis.getShopCode() + analysis.getPlatformSku() + analysis.getPlatformSaleCode());
            if (ObjUtil.isNotEmpty(id)) {
                analysis.setHeadId(id);
            }
        });

    }

    /**
     * 处理BD异常和Coupon异常标签
     * 通过listNeedUpdatePublishStatus获取数据，拿到platform_goods_id之后再去awsMongoTemplate获取ItemTempDay
     * 1：如果deal为空或者为0新增"BD异常"标签
     * 2：flag的coupon为空或者为0新增"Coupon异常"标签
     */
    @XxlJob("processItemTempDayLabels")
    public void processItemTempDayLabels() {
        log.info("开始处理BD异常、Coupon异常和广告受限标签");
        try {
            String today = DateUtils.getDate();
            listingLabelService.delYesterdayLabel("ADS-itemTempDay");
            // 监控今天是否有ItemTempDay数据生成
            boolean isDataGenerated = monitorItemTempDayDataGeneration(today);
            if (!isDataGenerated) {
                return;
            }

            Integer lastId = 0;
            int batchSize = 1000;
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 获取需要更新发布状态的商品列表
                List<GoodsHead> goodsHeadList = goodsHeadService.listNeedUpdatePublishStatus(lastId);

                if (CollUtil.isEmpty(goodsHeadList)) {
                    hasMoreData = false;
                    break;
                }

                // 处理当前批次数据
                processItemTempDayBatch(goodsHeadList, today);

                // 更新lastId为当前批次最后一个记录的ID
                lastId = goodsHeadList.get(goodsHeadList.size() - 1).getId();

                // 如果返回的数据少于批次大小，说明没有更多数据了
                if (goodsHeadList.size() < batchSize) {
                    hasMoreData = false;
                }
            }

            log.info("BD异常、Coupon异常和广告受限标签处理完成");
        } catch (Exception e) {
            log.error("处理BD异常、Coupon异常和广告受限标签时发生错误", e);
        }
    }

    /**
     * 处理单个批次的ItemTempDay和广告受限标签
     * @param goodsHeadList 商品头表列表
     */
    private void processItemTempDayBatch(List<GoodsHead> goodsHeadList, String today) {
        try {
            // 提取所有的ASIN
            List<String> asinList = goodsHeadList.stream()
                    .map(GoodsHead::getPlatformGoodsId)
                    .filter(StrUtil::isNotBlank)
                     .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(asinList)) {
                log.error("当前批次没有有效的ASIN数据");
                return;
            }

            // 查询当前时间在促销期间的ASIN促销明细信息
            Map<String, List<DwiPromotionsDetail>> asinPromotionMap = new HashMap<>();
            List<String> promotionAsinList = new ArrayList<>();
            try {
                List<DwiPromotionsDetail> promotionDetails = odsCrlCrlVcCatalogDataService.listPromotionDetailsByAsins(asinList);
                if (CollUtil.isNotEmpty(promotionDetails)) {
                    asinPromotionMap = promotionDetails.stream()
                            .collect(Collectors.groupingBy(DwiPromotionsDetail::getPlatformSaleCode));
                    promotionAsinList = promotionDetails.stream()
                            .map(DwiPromotionsDetail::getPlatformSaleCode)
                            .distinct()
                            .collect(Collectors.toList());
                    log.info("查询到{}个ASIN当前处于促销期间，促销明细记录数：{}", asinPromotionMap.size(), promotionDetails.size());
                }
            } catch (Exception e) {
                log.error("查询促销期间ASIN数据时发生错误", e);
            }

            // 查询ItemTempDay数据（只查询在促销期间的ASIN）
            Map<String, ItemTempDay> asinToItemTempDayMap = new HashMap<>();
            Set<String> restrictedAsinSet = new HashSet<>();

            try {
                if (CollUtil.isNotEmpty(promotionAsinList)) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("date").is(today)
                            .and("asin").in(promotionAsinList));
                    List<ItemTempDay> itemTempDayList = awsMongoTemplate.find(query, ItemTempDay.class);

                    if (CollUtil.isNotEmpty(itemTempDayList)) {
                        asinToItemTempDayMap = itemTempDayList.stream()
                                .collect(Collectors.toMap(ItemTempDay::getAsin, item -> item, (existing, replacement) -> existing));
                        log.info("成功查询到{}条ItemTempDay数据", itemTempDayList.size());
                    } else {
                        log.warn("MongoDB中没有找到对应的ItemTempDay数据，日期：{}, 促销ASIN数量：{}", today, promotionAsinList.size());
                    }
                } else {
                    log.info("没有ASIN处于促销期间，跳过ItemTempDay数据查询");
                }
            } catch (Exception e) {
                log.error("查询ItemTempDay数据时发生错误", e);
            }

            try {
                // 查询广告受限数据
                List<CrlRpaAdvertisingRestrictions> advertisingRestrictions = bi2DataService.selectAdvertisingRestrictionsByAsinList(asinList);
                if (CollUtil.isNotEmpty(advertisingRestrictions)) {
                    restrictedAsinSet = advertisingRestrictions.stream()
                            .map(CrlRpaAdvertisingRestrictions::getAsin)
                            .collect(Collectors.toSet());
                    log.info("成功查询到{}条广告受限数据", advertisingRestrictions.size());
                } else {
                    log.info("没有找到广告受限数据，ASIN数量：{}", asinList.size());
                }
            } catch (Exception e) {
                log.error("查询广告受限数据时发生错误", e);
            }

            // 生成标签（即使某一个数据源查询失败，也可以处理另一个数据源的标签）
            List<ListingLabel> labelsToInsert = generateLabelsFromItemTempDay(goodsHeadList, asinToItemTempDayMap, restrictedAsinSet, asinPromotionMap);

            // 批量插入标签
            if (CollUtil.isNotEmpty(labelsToInsert)) {
                // 先删除现有的BD异常、Coupon异常和广告受限标签
//                deleteExistingLabels(labelsToInsert);

                // 批量插入新标签
                Lists.partition(labelsToInsert, 1000).forEach(listingLabelService::insertListingLabelBatch);

                log.info("成功处理{}条标签数据", labelsToInsert.size());
            } else {
                log.info("当前批次没有生成任何标签数据");
            }

        } catch (Exception e) {
            log.error("处理ItemTempDay批次数据时发生错误", e);
        }
    }

    /**
     * 根据ItemTempDay数据和广告受限数据生成标签
     * @param goodsHeadList 商品头表列表
     * @param asinToItemTempDayMap ASIN到ItemTempDay的映射（可能为空）
     * @param restrictedAsinSet 广告受限的ASIN集合（可能为空）
     * @param asinPromotionMap ASIN到促销明细的映射（可能为空）
     * @return 生成的标签列表
     */
    private List<ListingLabel> generateLabelsFromItemTempDay(List<GoodsHead> goodsHeadList, Map<String, ItemTempDay> asinToItemTempDayMap, Set<String> restrictedAsinSet, Map<String, List<DwiPromotionsDetail>> asinPromotionMap) {
        List<ListingLabel> labels = new ArrayList<>();

        for (GoodsHead goodsHead : goodsHeadList) {
            String asin = goodsHead.getPlatformGoodsId();
            if (ObjUtil.isEmpty(asin)) {
                continue;
            }

            // 处理ItemTempDay相关标签（BD异常和Coupon异常）
            if (asinToItemTempDayMap != null && !asinToItemTempDayMap.isEmpty()) {
                ItemTempDay itemTempDay = asinToItemTempDayMap.get(asin);
                if (itemTempDay != null) {
                    // 获取该ASIN的促销信息
                    List<DwiPromotionsDetail> promotionDetails = asinPromotionMap.getOrDefault(asin, Collections.emptyList());

                    // 检查是否有BEST_DEAL类型的促销
                    boolean hasBestDealPromotion = promotionDetails.stream()
                            .anyMatch(detail -> "BEST_DEAL".equals(detail.getPromotionType()));

                    // 检查是否有CODED_COUPON类型的促销
                    boolean hasCodedCouponPromotion = promotionDetails.stream()
                            .anyMatch(detail -> "CODED_COUPON".equals(detail.getPromotionType()));

                    // BD异常逻辑：如果有BEST_DEAL促销，则检查ItemTempDay是否有BD数据，没有才认为是异常
                    boolean shouldCheckBdException = true;
                    if (hasBestDealPromotion) {
                        // 有BEST_DEAL促销，检查ItemTempDay是否有BD数据
                        String deal = itemTempDay.getDeal();
                        ItemTempDay.Flag flag = itemTempDay.getFlag();
                        String flagDeal = (flag != null) ? flag.getDeal() : null;

                        // 如果ItemTempDay中有BD数据（deal不为空且不为0），则不应该生成BD异常标签
                        if ((ObjUtil.isNotEmpty(deal) && !"0".equals(deal)) ||
                            (ObjUtil.isNotEmpty(flagDeal) && !"0".equals(flagDeal))) {
                            shouldCheckBdException = false;
                        }
                    }

                    if (shouldCheckBdException) {
                        // 检查deal字段，如果为空或者为0，新增"BD异常"标签
                        boolean isDealException = false;

                        // 检查itemTempDay的deal字段
                        String deal = itemTempDay.getDeal();
                        if (ObjUtil.isEmpty(deal) || "0".equals(deal)) {
                            isDealException = true;
                        }

                        // 检查flag对象和flag.deal字段
                        ItemTempDay.Flag flag = itemTempDay.getFlag();
                        if (flag == null) {
                            // 如果flag为null，也视为deal异常
                            isDealException = true;
                        } else {
                            // 检查flag的deal字段
                            String flagDeal = flag.getDeal();
                            if (ObjUtil.isEmpty(flagDeal) || "0".equals(flagDeal)) {
                                isDealException = true;
                            }
                        }

                        if (isDealException) {
                            ListingLabel bdLabel = createLabel(goodsHead, "BD异常");
                            labels.add(bdLabel);
                        }
                    }

                    // Coupon异常逻辑：如果有CODED_COUPON促销，则检查ItemTempDay是否有Coupon数据，没有才认为是异常
                    boolean shouldCheckCouponException = true;
                    if (hasCodedCouponPromotion) {
                        // 有CODED_COUPON促销，检查ItemTempDay是否有Coupon数据
                        ItemTempDay.Flag flag = itemTempDay.getFlag();
                        String coupon = (flag != null) ? flag.getCoupon() : null;

                        // 如果ItemTempDay中有Coupon数据（coupon不为空且不为0），则不应该生成Coupon异常标签
                        if (ObjUtil.isNotEmpty(coupon) && !"0".equals(coupon)) {
                            shouldCheckCouponException = false;
                        }
                    }

                    if (shouldCheckCouponException) {
                        // 检查flag的coupon字段，如果为空或者为0，新增"Coupon异常"标签
                        ItemTempDay.Flag flag = itemTempDay.getFlag();
                        if (flag != null) {
                            String coupon = flag.getCoupon();
                            if (ObjUtil.isEmpty(coupon) || "0".equals(coupon)) {
                                ListingLabel couponLabel = createLabel(goodsHead, "Coupon异常");
                                labels.add(couponLabel);
                            }
                        } else {
                            // flag为null，也认为是Coupon异常
                            ListingLabel couponLabel = createLabel(goodsHead, "Coupon异常");
                            labels.add(couponLabel);
                        }
                    }
                }
            }

            // 处理广告受限标签（独立于ItemTempDay数据）
            if (restrictedAsinSet != null && !restrictedAsinSet.isEmpty() && restrictedAsinSet.contains(asin)) {
                ListingLabel restrictedLabel = createLabel(goodsHead, "广告受限");
                labels.add(restrictedLabel);
            }
        }

        return labels;
    }

    /**
     * 创建标签对象
     * @param goodsHead 商品头表
     * @param labelValue 标签值
     * @return 标签对象
     */
    private ListingLabel createLabel(GoodsHead goodsHead, String labelValue) {
        ListingLabel label = new ListingLabel();
        label.setHeadId(goodsHead.getId());
        label.setPlatform(goodsHead.getPlatform());
        label.setSiteCode(goodsHead.getSiteCode());
        label.setShopCode(goodsHead.getShopCode());
        label.setLabelType("ADS-itemTempDay");
        label.setLabel(labelValue);
        label.setCreateTime(DateUtils.getNowDate());
        return label;
    }

    /**
     * 删除现有的BD异常、Coupon异常和广告受限标签
     * 无论哪种数据源查询失败，都会清理所有相关标签，确保数据一致性
     * @param labelsToInsert 要插入的标签列表
     */
    private void deleteExistingLabels(List<ListingLabel> labelsToInsert) {
        Set<Integer> headIds = labelsToInsert.stream()
                .map(ListingLabel::getHeadId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(headIds)) {
            return;
        }
        listingLabelService.deleteListingLabelsByHeadIds(Arrays.asList("BD异常", "Coupon异常", "广告受限"), headIds);

    }

    /**
     * 监控今天是否有ItemTempDay数据生成，如果没有数据则发送钉钉通知
     * @param date 查询日期
     */
    private boolean monitorItemTempDayDataGeneration(String date) {
        try {
            // 构建查询条件，查询今天是否有任何ItemTempDay数据
            Query query = new Query();
            query.addCriteria(Criteria.where("date").is(date));
            query.limit(1); // 只需要查询一条记录即可判断是否有数据

            // 查询今天是否有ItemTempDay数据
            List<ItemTempDay> todayData = awsMongoTemplate.find(query, ItemTempDay.class);

            // 如果今天没有任何ItemTempDay数据，发送钉钉通知
            if (CollUtil.isEmpty(todayData)) {
                sendItemTempDayGenerationAlert(date);
                return false;
            } else {
                log.info("今天({})的ItemTempDay数据正常，已检测到数据存在", date);
                return true;
            }

        } catch (Exception e) {
            log.error("监控ItemTempDay数据生成时发生错误", e);
        }
        return false;
    }

    /**
     * 发送ItemTempDay数据未生成的钉钉通知
     * @param date 查询日期
     */
    private void sendItemTempDayGenerationAlert(String date) {
        try {
            // 构建钉钉消息内容
            Map<String, String> featureMap = new HashMap<>();
            featureMap.put("查询日期", date);
            featureMap.put("数据状态", "未检测到任何ItemTempDay数据");
            featureMap.put("影响范围", "所有ASIN的BD异常标签生成将受到影响");
            featureMap.put("建议操作", "请检查外部数据源是否正常运行");

            // 发送钉钉通知
            String title = "ItemTempDay数据生成监控";
            String context = String.format("检测到今天(%s)没有生成任何ItemTempDay数据，请及时检查外部数据源", date);

            if (EnvUtils.isProdProfile()) {
                dingdingMonitorInfoBiz.monitorSend(title, ARRAY_GROUP_BD_DATA_NOT_GENERATED.getMonitorType(), context, featureMap);
            }
            log.warn("发送ItemTempDay数据未生成钉钉通知：日期={}", date);
        } catch (Exception e) {
            log.error("发送ItemTempDay数据未生成钉钉通知时发生错误", e);
        }
    }
}
